\documentclass[11pt]{article}
\usepackage[colorlinks=true, urlcolor=blue]{hyperref}
\usepackage[normalem]{ulem}
\usepackage[margin=0.7in]{geometry} % Adjust page margins as needed
\usepackage{times}                % Use Times New Roman font
\usepackage{hyperref}             % Hyperlinks in the document
\usepackage{enumitem}             % To customize itemize/enumerate spacing
\usepackage{caption}              % For better control of captions
\usepackage{graphicx}             % If you need to include figures
\usepackage{array}                % For better tabular handling
\usepackage{color}    
\usepackage{ulem} 
\usepackage{setspace}
\usepackage{enumitem}
\usepackage{float}
\usepackage[table,xcdraw]{xcolor}
\usepackage{booktabs}
\usepackage{geometry}
\usepackage{array}
\usepackage{fontawesome5}  % 提供 coffee 图标 ☕

\usepackage{multicol}
\usepackage{titlesec}
\usepackage{parskip}
\setlength{\parskip}{0.3em}

\newlist{tightitemize}{itemize}{1}
\setlist[tightitemize]{%
  label=\textendash,       
  itemsep=0pt,             
  %partopsep=0pt,           
  parsep=0pt,              
  leftmargin=1.5em        
}

\setlength{\parskip}{6pt}         % Spacing between paragraphs
\setlength{\parindent}{0pt}       % No paragraph indent

\begin{document}
\singlespacing
% ----------------------------------------------------------------------
% Changed the title font to \LARGE and bold
% ----------------------------------------------------------------------
\title{%
  \Large \bfseries 
  \textit{Proposal for IEEE WCNC 2026 Workshop (Half-day), Kuala Lumpur, Malaysia}\\[6pt]
  \rule{\textwidth}{0.5pt}\\[6pt]
 \normalsize {Workshop on Next-Generation Security Architectures and Privacy-Preserving Technologies for 6G \\
  (Tentative Date: TBD, Endorsed by the {\color{blue}\textit{ IEEE ComSoc ISAC-ETI}}})
}
\date{}   % 不显示日期
\maketitle

\vspace{-7em}
\section*{\uline{I: Workshop Organizers}}\vspace{-0.8em}

\textbf{Steering Committee Members} \vspace{-0.4em}
\begin{itemize}[label=\textendash, itemsep=0pt,topsep=0pt, leftmargin=1.5em]
\item Prof. Athina Petropulu, Rutgers University, USA
\item Prof. Christos Masouros, University College London, UK 
\item Prof. Shi Jin, Southeast University, China
\item Prof. Qinyu Zhang, Harbin Institute of Technology (Shenzhen), China
\item Prof. Wei Wang, Dalian University of Technology, China
\end{itemize}

\textbf{Workshop Co-chairs}\vspace{-0.4em}
\begin{itemize}[label=\textendash, itemsep=0pt,topsep=0pt, leftmargin=1.5em]
    \item \textbf{Fuwang Dong}, Harbin Engineering University, China 
    (\href{mailto:<EMAIL>}{\uline{<EMAIL>}})
    
    \item \textbf{Zhen Du}, Nanjing University of Information Science and Technology, China 
    (\href{mailto:<EMAIL>}{\uline{<EMAIL>}})
    
    \item \textbf{Nanchi Su}, Harbin Institute of Technology (Shenzhen), China  
    (\href{mailto:<EMAIL>}{\uline{<EMAIL>}})
    
    \item \textbf{Fan Liu}, Southeast University, China 
    (\href{mailto:<EMAIL>}{\uline{<EMAIL>}})

    \item \textbf{Tse-Tin Chan}, The Education University of Hong Kong, Hong Kong SAR, China    
    (\href{mailto:<EMAIL>}{\uline{<EMAIL>}})

\end{itemize}
\vspace{-1em}

\section*{\underline{II: Scope and Topics of the Workshop (Draft CFP)}}\vspace{-0.8em}

The sixth generation (6G) of wireless networks is poised to become the backbone of the intelligent information infrastructure of the future. By integrating advanced communications, sensing, computing, and control into a unified, adaptive, and programmable wireless environment, 6G is expected to support a wide range of transformative applications, including autonomous mobility, intelligent industrial systems, digital twins, edge artificial intelligence (AI), and immersive virtual worlds.

A key distinguishing feature of 6G, as acknowledged by major standardization bodies, including the International Telecommunication Union (ITU), 3rd Generation Partnership Project (3GPP), and Institute of Electrical and Electronics Engineers (IEEE), is its deep integration of AI and sensing capabilities directly into the communication fabric. Technologies such as integrated sensing and communications (ISAC), semantic-aware networking, near-field and terahertz transmission, extremely massive MIMO, and reconfigurable intelligent surfaces (RISs) are converging to create networks that are not only highly connected but also perceptive, context-aware, and adaptive.

However, the convergence of these technologies fundamentally reshapes the security and privacy landscape of wireless systems. In 6G networks, data is not only transmitted but also inferred, interpreted, and acted upon in real time and often autonomously. The integration of AI, sensing, and programmable environments creates new attack vectors where adversaries can exploit machine learning models, manipulate intelligent surfaces, inject semantic noise, or compromise the integrity of sensing-communication processes. Traditional security architectures, which rely primarily on higher-layer cryptographic protocols, are insufficient to address these multi-dimensional threats.

6G networks require next-generation security architectures that are built from the ground up with security and privacy as foundational design principles. These architectures must encompass physical-layer security mechanisms, AI-native threat detection, privacy-preserving protocols for sensing and communication, and adaptive security frameworks that can respond to dynamic and intelligent attacks. Furthermore, privacy-preserving technologies must evolve to protect not only communication content but also behavioral patterns, location information, and contextual data that can be inferred from the rich sensing capabilities of 6G systems.

This workshop aims to bring together researchers from academia and industry to explore the latest breakthroughs in next-generation security architectures and privacy-preserving technologies for 6G networks. Through knowledge exchange and collaborative discussions, we seek to advance research and development in this critical field, fostering innovation in secure and privacy-aware 6G system design and deployment.

\newpage
\textbf{Topics of interest} include but are not limited to:

\begin{tightitemize}
\item Next-generation security architectures for 6G networks
\item Zero-trust security frameworks for programmable wireless environments
\item AI-native security mechanisms and threat detection systems
\item Privacy-preserving technologies for sensing-communication integration
\item Secure reconfigurable intelligent surfaces (RIS) and metasurfaces
\item Physical-layer security for mmWave, THz, and near-field communications
\item Quantum-enhanced security protocols for 6G networks
\item Semantic-aware security and privacy protection mechanisms
\item Secure edge computing and distributed AI in 6G
\item Privacy-preserving federated learning for wireless networks
\item Adversarial machine learning attacks and defenses in 6G
\item Differential privacy in 6G sensing and communication systems
\item Secure network slicing and multi-tenant architectures
\item Privacy-preserving location services and positioning systems
\item Trustworthy AI for autonomous network operations
\item Cross-layer security designs for 6G protocol stacks
\item Secure digital twin and metaverse applications
\item Privacy-preserving data analytics and inference in 6G
\item Blockchain and distributed ledger technologies for 6G security
\item Post-quantum cryptography for future wireless networks
\item Security and privacy metrics for 6G performance evaluation
\item Benchmarking frameworks for secure 6G system assessment
\end{tightitemize}

\vspace{-1em}
\section*{\underline{III: A Description of Past Versions of the workshop}}\vspace{-0.8em}
The past versions of the workshop were successfully held in ICCC'2019/2021/2023/2024, PIMRC'2023/2024, ICASSP '2022/2024, Globecom'2019/2023, ICC'2020/2021/2022/2023/2024, WCNC'2021/2022/2023/2024 with 36.3 avg. submissions per workshop and more than 120 max. attendees. The number of submissions is more than 40 in the most recent conference, IEEE ICC 2024, while that of accepted papers is 18, with an acceptance rate of less than 45\%.

\vspace{-1em}
\section*{\underline{IV: Rationale (Why is the topic current and important?)}}\vspace{-0.8em}

The rapid emergence of 6G wireless networks, characterized by the deep integration of AI, sensing, and programmable environments, is reshaping the security and privacy landscape of wireless communications. Unlike previous generations, 6G networks are designed to be perceptive, context-aware, and adaptive, creating unprecedented opportunities for both enhanced functionality and new security vulnerabilities.

The significance of security and privacy in 6G extends beyond traditional communication protection. With technologies such as ISAC, RIS, semantic communications, and AI-native networking becoming foundational elements, the attack surface has expanded dramatically. Adversaries can now exploit joint communication-sensing signals, manipulate intelligent surfaces, inject semantic noise, or poison AI models embedded in the network infrastructure.

Major standardization efforts are actively addressing these challenges: IEEE 802.11bf is defining Wi-Fi-based sensing protocols with security considerations; 3GPP has launched ISAC studies in Release 19 with security implications; and ETSI's Industry Specification Group on ISAC is laying technical groundwork that includes security frameworks. The IEEE ComSoc ISAC Emerging Technology Initiative (ISAC-ETI) has identified security as a critical research priority for the ISAC ecosystem.

Despite growing attention to AI-native wireless security and ISAC system design, there remains a significant gap in research that takes a holistic, system-wide perspective on security for 6G networks. Current approaches often address security as an afterthought rather than a foundational design principle, leading to vulnerabilities that can compromise the entire network ecosystem.

This workshop aims to bridge this gap by bringing together researchers and practitioners from academia and industry to explore resilient and trustworthy security architectures tailored for 6G smart wireless environments. By fostering collaboration and advancing state-of-the-art solutions, the workshop seeks to accelerate both research progress and practical adoption of secure 6G technologies.

\vspace{-1em}
\section*{\underline{V: Publicity and Promotion Plan}}\vspace{-0.8em}
The CFP will be advertised in numerous relevant events and venues to reach potential attendees and contributors, such as the mail list (Comsoc, SPS, AES, CCF, HEU, HIT, etc), social networks: LinkedIn, ResearchGate, Twitter, Facebook, WeChat, personal blogs, etc. The chairs will send a chair email to ISAC-ETI mailing list. In order to fully use these channels, we propose a three-stage publicity plan:
\begin{itemize}[label=\textendash, itemsep=0pt, leftmargin=1.5em]
\item[1.] \textbf{[Sep. 1 - Oct. 1] Informing potential authors.} Online the website when the proposal is granted. A poster will be sent from the mailing lists, such as ComSoc, SPS, CCF, and the organizer's personal network. The organizers will include the
workshop information into their slide when they are attending other conferences.

\item[2.]  \textbf{[Oct. 1 -Paper DDL] Notify the deadlines to promote submissions.} A new poster that highlights the submission deadline will be sent from the mailing lists and the organizer's personal network. A press release will be published on social medias, such as personal blog, LinkedIn, twitter and Wechat blog.

\item[3.]\textbf{[Paper DDL – Conference Date] Attract the attendees.} A series of press releases will be published on social media. These press releases will shortly describe the importance of accepted works and the confirmed world famous attendees, in order to
attract more attendees.
\end{itemize}

\vspace{-1em}
\section*{\underline{VI: Biographies of Workshop Organizers}}\vspace{-0.8em}

\textbf{Fuwang Dong} (Member, IEEE) is currently an Associate Professor with the College of Intelligent Systems Science and Technology, Harbin Engineering University (HEU), Harbin, China. Prior to that, he was a Post-Doctoral Researcher with the Southern University of Science and Technology, Shenzhen, China, from 2022 to 2024. He received his B.Sc., M.Sc., and Ph.D. degrees from HEU, Harbin, China, in 2014, 2017, and 2022, respectively. He was the recipient of the IEEE/CIC ICCC Best Paper Award and the Best Ph.D. Thesis Award of HEU and Heilongjiang Association for Artificial Intelligence in 2023. He has been named an Exemplary Reviewer for IEEE TGCN 2022 and IEEE COMML 2023. His research interests include integrated sensing and communications (ISAC), array signal processing, and intelligent sensing systems. \\\vspace{-1em}

\textbf{Zhen Du} (Member, IEEE) is currently a lecturer with the School of Electronic and Information Engineering, Nanjing University of Information Science and Technology, Nanjing, China. He received the B.Eng. degree in communication engineering from Northwestern Polytechnical University, Xi'an, China, in 2016 and the Ph.D. degree in information and communication engineering from Shanghai Jiao Tong University, Shanghai, China, in 2022. His research interests include integrated sensing and communication, beam management in vehicular networks, and radar signal processing.  \\\vspace{-1em}

\textbf{Nanchi Su} (Member, IEEE) is currently an associate research fellow with the Guangdong Provincial Key Laboratory of Aerospace Communication and Networking Technology, Harbin Institute of Technology (Shenzhen), Shenzhen, China. She received the B.Eng. and M.Eng. from Harbin Institute of Technology, Harbin, China, in 2015 and 2018, respectively. She received the Ph.D. degree from University College London, London, UK, in 2023. Her research interests include integrated sensing and communication (ISAC), signal processing, network security, space-air-ground integrated network (SARGIN), and situational awareness.
\\\vspace{-1em}

\textbf{Fan Liu} (Senior Member, IEEE) is currently a Professor with the School of Information Science and Engineering, Southeast University, Nanjing, China. He has 10 papers selected as IEEE ComSoc Best Readings. He is the Founding Academic Chair of the IEEE ComSoc ISAC Emerging Technology Initiative (ISAC-ETI), an Elected Member of the IEEE SPS Sensor Array and Multichannel Technical Committee (SAM-TC), the Founding Member of the IEEE SPS ISAC Technical Working Group (ISACTWG), an Associate Editor of IEEE TCOM, IEEE TMC, and a Guest Editor of IEEE JSAC, etc. He has served as the Organizer and Co-Chair for several workshops, special sessions and tutorials in flagship IEEE conferences, including ICC, GLOBECOM, ICASSP, SPAWC, and MobiCom. He is a TPC Co-Chair of the 2nd-4th IEEE Joint Communication and Sensing (JC\&S) Symposium, a Symposium Co-Chair for IEEE GLOBECOM 2023, and a Track Co-Chair for the IEEE WCNC 2024. He is an IMT-2030 (6G) ISAC Task Group member. He received the 2023 IEEE Communications Society Stephan O. Rice Prize, the 2023 IEEE ICC Best Paper Award, the 2023 IEEE/CIC ICCC 2023 Best Paper Award, the 2021 IEEE Signal Processing Society Young Author Best Paper Award, the 2019 Best Ph.D. Thesis Award of Chinese Institute of Electronics, etc. \\\vspace{-1em}

\textbf{Tse-Tin Chan} (Member, IEEE) received the B.Eng. (First Class Hons.) and Ph.D. degrees in Information Engineering from The Chinese University of Hong Kong (CUHK), Hong Kong SAR, China, in 2014 and 2020, respectively. He is currently an Assistant Professor with the Department of Mathematics and Information Technology, The Education University of Hong Kong (EdUHK), Hong Kong SAR, China. From 2020 to 2022, he was an Assistant Professor with the Department of Computer Science, The Hang Seng University of Hong Kong (HSUHK), Hong Kong SAR, China. His research interests include wireless communications and networking, Internet of Things (IoT), age of information (AoI), and artificial intelligence (AI)-native wireless communications. He has authored more than 50 papers in leading journals and conferences, including IEEE TCOM, IEEE TMC, IEEE TAP, IEEE T-MTT, and IEEE TCCN, among others. He is the Principal Investigator of several externally funded research projects, with total funding exceeding USD 5 million, covering research topics such as ultra-reliable low-latency vehicle-to-everything (V2X) communications, time-sensitive wireless body-area networks, and intelligent wireless edge networks. He has served as a Technical Program Committee member and reviewer for flagship IEEE conferences, including IEEE ICC and IEEE GLOBECOM.

\vspace{-1em}
\section*{\underline{VII: Participants}}\vspace{-0.5em}

\textbf{Technical Program Committee Members (Tentative):}\vspace{-1em}
\begin{multicols}{2}
\begin{flushleft}
George C. Alexandropoulos, Univ. of Athens \\
Christos Masouros, Univ. College London \\
Aylin Yener, The Ohio State Univ. \\
Ahmad Bazzi, NYU Abu Dhabi \\
Zhongxiang Wei, Tongji Univ. \\
Moeness G. Amin, Villanova Univ. \\
Aboulnasr Hassanien, Wright State Univ. \\
Xiaodong Wang, Columbia Univ. \\
John Thompson, Univ. of Edinburgh \\
Tharmalingam Ratnarajah, Univ. of Edinburgh \\
Yimin D. Zhang, Temple Univ. \\
Hongbin Li, Stevens Inst. of Tech. \\
Dmitriy Garmatyuk, Miami Univ. \\
Jinhong Yuan, Univ. of New South Wales \\
Weijie Yuan, Univ. of New South Wales \\
Michel Matthaiou, Queen's Univ. Belfast \\

Ang Li, Univ. of Sydney \\
Xianghao Yu, FAU Erlangen-Nürnberg \\
Shuowen Zhang, National Univ. of Singapore \\
Kumar Vijay Mishra, Univ. of Iowa \\
Matthew Ritchie, Univ. College London \\
Zesong Fei, Beijing Inst. of Tech. \\
Yonghui Li, Univ. of Sydney \\
Le Zheng, Aptiv \\
Bo Li, Qualcomm \\
Jing Huang, Qualcomm \\
I. B. Collings, Macquarie Univ. \\
J. Leez, Samsung Electronics \\
Amitav Mukherjee, Ericsson \\
Ranveer Chandra, Microsoft Research \\
H. C. Papadopoulos, DOCOMO USA Lab \\
Xiaojing Huang, Univ. of Technology Sydney \\
\end{flushleft}
\end{multicols}

\vspace{-1.8em}
\section*{\underline{VIII: Planned Format of the Workshop}}\vspace{-0.8em}
The workshop is organized as a half-day program with a tentative schedule shown below. The potential participants of the panel discussion are world-class researchers from both academia and industry and workshop authors. The panel discussion will focus on 8 selected next-generation security and privacy questions for 6G networks, such as ``zero-trust architectures for 6G'', ``privacy-preserving technologies in sensing-communication'', ``quantum-enhanced security protocols'', ``AI-native threat detection systems'', etc.

\begin{center}
    {\textbf{Tentative Workshop Schedule}}
\end{center}
\vspace{-1em}
\renewcommand{\arraystretch}{1.4}  % 行距
\setlength{\tabcolsep}{10pt}       % 列间距

\begin{center}
\rowcolors{3}{white}{gray!10}  % 隔行灰色背景
\begin{tabular}{>{\centering\arraybackslash}p{3cm} >{\centering\arraybackslash}p{5cm} >{\centering\arraybackslash}p{3cm} >{\centering\arraybackslash}p{5cm}}
\rowcolor{blue!15}
\textbf{Time} & \textbf{Activity} & \textbf{Time} & \textbf{Activity} \\
09:00--09:10 & Welcome Speech & 09:10--09:30 & Oral Session 1 \\
09:30--09:50 & Oral Session 2 & 09:50--10:10 & Oral Session 3 \\
\multicolumn{4}{c}{\cellcolor{orange!20}\textbf{\faCoffee~ Coffee Break}} \\
10:30--10:50 & Oral Session 4 & 10:50--11:10 & Oral Session 5 \\
11:10--11:30 & Oral Session 6 & 11:30--11:50 & Oral Session 7 \\
11:50--12:10 & Oral Session 8 & & \\
\bottomrule
\end{tabular}
\end{center}

\textbf{Review Process:} All papers submitted to this workshop will undergo a rigorous peer-review process according to ComSoc's academic best practices. Each paper will be assigned to at least three leading experts in the field as reviewers. There will be one review round, and conditional acceptance will only be granted to manuscripts requiring moderate revisions. The review criteria will include technical quality, novelty, relevance to the workshop scope, and clarity of presentation.

\section*{\underline{IX: Expected Volumes of Papers and Attendees}}\vspace{-0.8em}
The CFP will be advertised in various relevant communities in order to reach out to potential authors (e.g., academic researchers, wireless security engineers, AI/ML engineers, network security specialists, etc.) and will be circulated by the TPC members who are based in different regions across the world. Moreover, we expect at least 80 attendees due to the importance of the technical areas addressed and the participation of world-class speakers. Overall, we expect around 20 submissions, where we plan to accommodate 8 papers for oral presentations. Hence, the acceptance rate will be at 40\%.

\section*{\underline{X: Workshop Website}}\vspace{-0.8em}
The workshop website will be hosted at: \textbf{https://6g-security-workshop.github.io/wcnc2026/} (to be established upon acceptance of the proposal). The website will include the call for papers, important dates, submission guidelines, program committee information, and registration details.

\end{document}
