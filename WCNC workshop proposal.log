This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.4.28)  1 AUG 2025 18:33
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"WCNC workshop proposal"
(./WCNC workshop proposal.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count192
\c@section=\count193
\c@subsection=\count194
\c@subsubsection=\count195
\c@paragraph=\count196
\c@subparagraph=\count197
\c@figure=\count198
\c@table=\count199
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX
 (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (/usr/local/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (/usr/local/texlive/2025/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count266
) (/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count267
) (/usr/local/texlive/2025/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen142
\Hy@linkcounter=\count268
\Hy@pagecounter=\count269
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count270
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count271
 (/usr/local/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen143
 (/usr/local/texlive/2025/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count272
\Field@Width=\dimen144
\Fld@charsize=\dimen145
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring ON on input line 6060.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count273
\c@Item=\count274
\c@Hfootnote=\count275
)
Package hyperref Info: Driver (autodetected): hxetex.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2024-11-05 v7.01l Hyperref driver for XeTeX
\pdfm@box=\box52
\c@Hy@AnnotLevel=\count276
\HyField@AnnotCount=\count277
\Fld@listcount=\count278
\c@bookmark@seq@number=\count279
 (/usr/local/texlive/2025/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (/usr/local/texlive/2025/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip51
) (/usr/local/texlive/2025/texmf-dist/tex/generic/ulem/ulem.sty
\UL@box=\box53
\UL@hyphenbox=\box54
\UL@skip=\skip52
\UL@hook=\toks18
\UL@height=\dimen146
\UL@pe=\count280
\UL@pixel=\dimen147
\ULC@box=\box55
Package: ulem 2019/11/18
\ULdepth=\dimen148
) (/usr/local/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count281
\Gm@cntv=\count282
\c@Gm@tempcnt=\count283
\Gm@bindingoffset=\dimen149
\Gm@wd@mp=\dimen150
\Gm@odd@mp=\dimen151
\Gm@even@mp=\dimen152
\Gm@layoutwidth=\dimen153
\Gm@layoutheight=\dimen154
\Gm@layouthoffset=\dimen155
\Gm@layoutvoffset=\dimen156
\Gm@dimlist=\toks19
) (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
) (/usr/local/texlive/2025/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip53
\enit@outerparindent=\dimen157
\enit@toks=\toks20
\enit@inbox=\box56
\enit@count@id=\count284
\enitdp@description=\count285
) (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen158
\captionmargin=\dimen159
\caption@leftmargin=\dimen160
\caption@rightmargin=\dimen161
\caption@width=\dimen162
\caption@indent=\dimen163
\caption@parindent=\dimen164
\caption@hangindent=\dimen165
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count286
\c@continuedfloat=\count287
Package caption Info: hyperref package is loaded.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen166
\Gin@req@width=\dimen167
) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen168
\ar@mcellbox=\box57
\extrarowheight=\dimen169
\NC@list=\toks21
\extratabsurround=\skip54
\backup@length=\skip55
\ar@cellbox=\box58
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/color.sty
Package: color 2024/06/23 v1.3e Standard LaTeX Color (DPC)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: xetex.def on input line 149.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)) (/usr/local/texlive/2025/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
) (/usr/local/texlive/2025/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count288
\float@exts=\toks22
\float@box=\box59
\@float@everytoks=\toks23
\@floatcapt=\box60
) (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
LaTeX Info: Redefining \color on input line 762.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/colortbl/colortbl.sty
Package: colortbl 2024/07/06 v1.0i Color table columns (DPC)
\everycr=\toks24
\minrowclearance=\skip56
\rownum=\count289
) (/usr/local/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen170
\lightrulewidth=\dimen171
\cmidrulewidth=\dimen172
\belowrulesep=\dimen173
\belowbottomsep=\dimen174
\aboverulesep=\dimen175
\abovetopsep=\dimen176
\cmidrulesep=\dimen177
\cmidrulekern=\dimen178
\defaultaddspace=\dimen179
\@cmidla=\count290
\@cmidlb=\count291
\@aboverulesep=\dimen180
\@belowrulesep=\dimen181
\@thisruleclass=\count292
\@lastruleclass=\count293
\@thisrulewidth=\dimen182
) (/usr/local/texlive/2025/texmf-dist/tex/latex/fontawesome5/fontawesome5.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count294
\l__pdf_internal_box=\box61
\g__pdf_backend_annotation_int=\count295
\g__pdf_backend_link_int=\count296
))
Package: fontawesome5 2022/05/02 v5.15.4 Font Awesome 5
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2024-08-16 LaTeX2e option processing using LaTeX3 keys
) (/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
) (/usr/local/texlive/2025/texmf-dist/tex/latex/fontawesome5/fontawesome5-utex-helper.sty
Package: fontawesome5-utex-helper 2022/05/02 v5.15.4 uTeX helper for fontawesome5
LaTeX Font Info:    Trying to load font information for TU+fontawesomefree on input line 69.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/fontawesome5/tufontawesomefree.fd)
LaTeX Font Info:    Trying to load font information for TU+fontawesomebrands on input line 70.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/fontawesome5/tufontawesomebrands.fd))) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2024/09/14 v1.9i multicolumn formatting (FMi)
\c@tracingmulticols=\count297
\mult@box=\box62
\multicol@leftmargin=\dimen183
\c@unbalance=\count298
\c@collectmore=\count299
\doublecol@number=\count300
\multicoltolerance=\count301
\multicolpretolerance=\count302
\full@width=\dimen184
\page@free=\dimen185
\premulticols=\dimen186
\postmulticols=\dimen187
\multicolsep=\skip57
\multicolbaselineskip=\skip58
\partial@page=\box63
\last@line=\box64
\mc@boxedresult=\box65
\maxbalancingoverflow=\dimen188
\mult@rightbox=\box66
\mult@grightbox=\box67
\mult@firstbox=\box68
\mult@gfirstbox=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\@tempa=\box99
\@tempa=\box100
\@tempa=\box101
\@tempa=\box102
\@tempa=\box103
\@tempa=\box104
\@tempa=\box105
\c@minrows=\count303
\c@columnbadness=\count304
\c@finalcolumnbadness=\count305
\last@try=\dimen189
\multicolovershoot=\dimen190
\multicolundershoot=\dimen191
\mult@nat@firstbox=\box106
\colbreak@box=\box107
\mc@col@check@num=\count306
) (/usr/local/texlive/2025/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box108
\beforetitleunit=\skip59
\aftertitleunit=\skip60
\ttl@plus=\dimen192
\ttl@minus=\dimen193
\ttl@toksa=\toks25
\titlewidth=\dimen194
\titlewidthlast=\dimen195
\titlewidthfirst=\dimen196
) (/usr/local/texlive/2025/texmf-dist/tex/latex/parskip/parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments
)
\enitdp@tightitemize=\count307
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 39.
LaTeX Font Info:    No file TUptm.fd. on input line 39.


LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 39.

(./WCNC workshop proposal.aux)
\openout1 = `"WCNC workshop proposal.aux"'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 39.
LaTeX Font Info:    ... okay on input line 39.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 39.
LaTeX Font Info:    ... okay on input line 39.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 39.
LaTeX Font Info:    ... okay on input line 39.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 39.
LaTeX Font Info:    ... okay on input line 39.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 39.
LaTeX Font Info:    ... okay on input line 39.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 39.
LaTeX Font Info:    ... okay on input line 39.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 39.
LaTeX Font Info:    ... okay on input line 39.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 39.
LaTeX Font Info:    ... okay on input line 39.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 39.
LaTeX Font Info:    ... okay on input line 39.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 39.
LaTeX Font Info:    ... okay on input line 39.
Package hyperref Info: Link coloring ON on input line 39.
 (./WCNC workshop proposal.out) (./WCNC workshop proposal.out)
\@outlinefile=\write3
\openout3 = `"WCNC workshop proposal.out"'.


*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(50.58878pt, 513.11745pt, 50.58878pt)
* v-part:(T,H,B)=(50.58878pt, 693.79243pt, 50.58878pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=513.11745pt
* \textheight=693.79243pt
* \oddsidemargin=-21.68121pt
* \evensidemargin=-21.68121pt
* \topmargin=-58.68121pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=59.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package caption Info: Begin \AtBeginDocument code.
Package caption Info: float package is loaded.
Package caption Info: End \AtBeginDocument code.

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 53.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/b/n' instead on input line 53.

LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <12> on input line 53.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <8> on input line 53.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <6> on input line 53.

LaTeX Warning: No \author given.

LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <14.4> on input line 86.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <7> on input line 86.

Overfull \hbox (0.48232pt too wide) in paragraph at lines 88--89
\TU/ptm/b/it/10.95 The sixth generation (6G) of wireless networks represents a paradigm shift towards intelligent, programmable,
 []



[1

]

[2]

[3]

[4]
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <10.95> on input line 225.

Overfull \hbox (22.12663pt too wide) in paragraph at lines 225--236
 [] 
 []



[5]

[6] (./WCNC workshop proposal.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.

Package rerunfilecheck Info: File `"WCNC workshop proposal".out' has not changed.
(rerunfilecheck)             Checksum: <no file>.
 ) 
Here is how much of TeX's memory you used:
 13307 strings out of 473832
 210436 string characters out of 5729481
 639477 words of memory out of 5000000
 36220 multiletter control sequences out of 15000+600000
 563916 words of font info for 59 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 72i,9n,79p,1401b,400s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on WCNC workshop proposal.pdf (6 pages).
